2025-07-31 23:36:07,437 - __main__ - INFO - ============================================================
2025-07-31 23:36:07,447 - __main__ - INFO - BUSINESS DATA COLLECTION STARTED
2025-07-31 23:36:07,447 - __main__ - INFO - ============================================================
2025-07-31 23:36:07,447 - __main__ - INFO - Starting Google Places API collection...
2025-07-31 23:36:07,448 - collectors.google_places - INFO - Searching for restaurant businesses...
2025-07-31 23:36:07,650 - collectors.google_places - ERROR - Request error for restaurant: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:08,653 - collectors.google_places - INFO - Searching for store businesses...
2025-07-31 23:36:08,720 - collectors.google_places - ERROR - Request error for store: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:09,720 - collectors.google_places - INFO - Searching for establishment businesses...
2025-07-31 23:36:09,775 - collectors.google_places - ERROR - Request error for establishment: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:10,776 - collectors.google_places - INFO - Searching for food businesses...
2025-07-31 23:36:10,833 - collectors.google_places - ERROR - Request error for food: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:11,834 - collectors.google_places - INFO - Searching for lodging businesses...
2025-07-31 23:36:11,889 - collectors.google_places - ERROR - Request error for lodging: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:12,891 - collectors.google_places - INFO - Searching for health businesses...
2025-07-31 23:36:12,950 - collectors.google_places - ERROR - Request error for health: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:13,951 - collectors.google_places - INFO - Searching for finance businesses...
2025-07-31 23:36:14,004 - collectors.google_places - ERROR - Request error for finance: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:15,005 - collectors.google_places - INFO - Searching for beauty_salon businesses...
2025-07-31 23:36:15,049 - collectors.google_places - ERROR - Request error for beauty_salon: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:16,050 - collectors.google_places - INFO - Searching for car_repair businesses...
2025-07-31 23:36:16,259 - collectors.google_places - ERROR - Request error for car_repair: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:17,260 - collectors.google_places - INFO - Searching for gym businesses...
2025-07-31 23:36:17,440 - collectors.google_places - ERROR - Request error for gym: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:18,441 - collectors.google_places - INFO - Searching for laundry businesses...
2025-07-31 23:36:18,670 - collectors.google_places - ERROR - Request error for laundry: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:19,671 - collectors.google_places - INFO - Searching for lawyer businesses...
2025-07-31 23:36:19,720 - collectors.google_places - ERROR - Request error for lawyer: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:20,721 - collectors.google_places - INFO - Searching for real_estate_agency businesses...
2025-07-31 23:36:20,766 - collectors.google_places - ERROR - Request error for real_estate_agency: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:21,766 - collectors.google_places - INFO - Searching for veterinary_care businesses...
2025-07-31 23:36:21,809 - collectors.google_places - ERROR - Request error for veterinary_care: 403 Client Error: Forbidden for url: https://places.googleapis.com/v1/places:searchNearby
2025-07-31 23:36:22,810 - collectors.google_places - INFO - Collected 0 businesses from Google Places API
2025-07-31 23:36:22,810 - __main__ - INFO - Google Places API: Collected 0 businesses
2025-07-31 23:36:22,811 - __main__ - INFO - Starting Overpass API collection...
2025-07-31 23:36:22,815 - collectors.overpass_api - INFO - Collecting amenities from Overpass API...
2025-07-31 23:36:27,400 - collectors.overpass_api - INFO - Collecting shops from Overpass API...
2025-07-31 23:36:33,739 - collectors.overpass_api - INFO - Collected 5450 businesses from Overpass API
2025-07-31 23:36:33,739 - __main__ - INFO - Overpass API: Collected 5450 businesses
2025-07-31 23:36:33,740 - __main__ - INFO - Starting Yellow Pages collection...
2025-07-31 23:36:33,740 - collectors.yellow_pages - INFO - Searching Yellow Pages for restaurants in Pittsburgh, PA...
2025-07-31 23:36:34,125 - collectors.yellow_pages - ERROR - Request error for restaurants page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=restaurants&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:36,127 - collectors.yellow_pages - INFO - Searching Yellow Pages for retail stores in Pittsburgh, PA...
2025-07-31 23:36:36,160 - collectors.yellow_pages - ERROR - Request error for retail stores page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=retail+stores&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:38,161 - collectors.yellow_pages - INFO - Searching Yellow Pages for auto repair in Pittsburgh, PA...
2025-07-31 23:36:38,197 - collectors.yellow_pages - ERROR - Request error for auto repair page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=auto+repair&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:40,198 - collectors.yellow_pages - INFO - Searching Yellow Pages for beauty salons in Pittsburgh, PA...
2025-07-31 23:36:40,234 - collectors.yellow_pages - ERROR - Request error for beauty salons page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=beauty+salons&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:42,236 - collectors.yellow_pages - INFO - Searching Yellow Pages for medical services in Pittsburgh, PA...
2025-07-31 23:36:42,279 - collectors.yellow_pages - ERROR - Request error for medical services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=medical+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:44,281 - collectors.yellow_pages - INFO - Searching Yellow Pages for professional services in Pittsburgh, PA...
2025-07-31 23:36:44,314 - collectors.yellow_pages - ERROR - Request error for professional services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=professional+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:46,315 - collectors.yellow_pages - INFO - Searching Yellow Pages for home services in Pittsburgh, PA...
2025-07-31 23:36:46,355 - collectors.yellow_pages - ERROR - Request error for home services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=home+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:48,356 - collectors.yellow_pages - INFO - Searching Yellow Pages for financial services in Pittsburgh, PA...
2025-07-31 23:36:48,391 - collectors.yellow_pages - ERROR - Request error for financial services page 1: 403 Client Error: Forbidden for url: https://www.yellowpages.com/search?search_terms=financial+services&geo_location_terms=Pittsburgh%2C+PA&page=1
2025-07-31 23:36:50,392 - collectors.yellow_pages - INFO - Collected 0 businesses from Yellow Pages
2025-07-31 23:36:50,392 - __main__ - INFO - Yellow Pages: Collected 0 businesses
2025-07-31 23:36:50,394 - __main__ - INFO - Total businesses collected from all sources: 5450
2025-07-31 23:36:50,395 - __main__ - INFO - Starting processing of 5450 businesses...
2025-07-31 23:36:50,396 - __main__ - INFO - Filtering businesses...
2025-07-31 23:36:50,399 - utils.filtering - INFO - Starting filtering of 5450 businesses...
2025-07-31 23:36:50,576 - utils.filtering - INFO - After filtering: 2590 businesses remain
2025-07-31 23:36:50,577 - __main__ - INFO - Deduplicating businesses...
2025-07-31 23:36:50,577 - utils.deduplication - INFO - Starting deduplication of 2590 businesses...
2025-07-31 23:36:50,940 - utils.deduplication - INFO - After exact duplicate removal: 2563 businesses
2025-07-31 23:38:53,752 - utils.deduplication - INFO - After fuzzy duplicate removal: 2349 businesses
2025-07-31 23:38:53,753 - __main__ - INFO - Processing complete: 2349 unique businesses
2025-07-31 23:38:53,753 - __main__ - INFO - Exporting results...
2025-07-31 23:38:53,811 - utils.data_export - INFO - Exported 2349 businesses to output\pittsburgh_businesses.csv
2025-07-31 23:38:53,812 - __main__ - INFO - CSV exported to: output\pittsburgh_businesses.csv
2025-07-31 23:38:53,857 - utils.data_export - INFO - Exported 2349 businesses to output\businesses_20250731_233853.json
2025-07-31 23:38:53,857 - __main__ - INFO - JSON exported to: output\businesses_20250731_233853.json
2025-07-31 23:38:53,862 - utils.data_export - INFO - Exported summary report to output\business_summary_20250731_233853.txt
2025-07-31 23:38:53,862 - __main__ - INFO - Summary report exported to: output\business_summary_20250731_233853.txt
2025-07-31 23:38:53,862 - __main__ - INFO - ============================================================
2025-07-31 23:38:53,863 - __main__ - INFO - BUSINESS DATA COLLECTION COMPLETED
2025-07-31 23:38:53,863 - __main__ - INFO - ============================================================
2025-07-31 23:38:53,863 - __main__ - INFO - Total runtime: 0:02:46.415274
2025-07-31 23:38:53,864 - __main__ - INFO - Raw businesses collected: 5450
2025-07-31 23:38:53,864 - __main__ - INFO - Final processed businesses: 2349
2025-07-31 23:38:53,864 - __main__ - INFO - Final breakdown by source:
2025-07-31 23:38:53,865 - __main__ - INFO -   overpass_api: 2349 businesses

"""
Yellow Pages scraper for business data.
"""
import requests
from bs4 import BeautifulSoup
import time
import logging
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote_plus
from config import YELLOW_PAGES_DELAY

logger = logging.getLogger(__name__)


class YellowPagesCollector:
    """Collector for business data from Yellow Pages website."""
    
    def __init__(self):
        """Initialize the Yellow Pages collector."""
        self.base_url = "https://www.yellowpages.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def collect_businesses(self, 
                          location: str = "Pittsburgh, PA",
                          categories: List[str] = None) -> List[Dict[str, Any]]:
        """Collect business data from Yellow Pages.
        
        Args:
            location: Location to search in
            categories: List of business categories to search for
            
        Returns:
            List of business data dictionaries
        """
        if categories is None:
            categories = [
                "restaurants",
                "retail stores",
                "auto repair",
                "beauty salons",
                "medical services",
                "professional services",
                "home services",
                "financial services"
            ]
        
        businesses = []
        
        for category in categories:
            logger.info(f"Searching Yellow Pages for {category} in {location}...")
            
            try:
                category_businesses = self._search_category(category, location)
                businesses.extend(category_businesses)
                
                # Rate limiting - be respectful
                time.sleep(YELLOW_PAGES_DELAY)
                
            except Exception as e:
                logger.error(f"Error searching for {category}: {str(e)}")
                continue
                
        logger.info(f"Collected {len(businesses)} businesses from Yellow Pages")
        return businesses
    
    def _search_category(self, category: str, location: str, max_pages: int = 3) -> List[Dict[str, Any]]:
        """Search for businesses in a specific category.
        
        Args:
            category: Business category to search for
            location: Location to search in
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of business data dictionaries
        """
        businesses = []
        
        for page in range(1, max_pages + 1):
            try:
                page_businesses = self._scrape_search_page(category, location, page)
                businesses.extend(page_businesses)
                
                # If we got fewer results than expected, we've reached the end
                if len(page_businesses) < 10:  # Typical page size
                    break
                    
                # Rate limiting between pages
                time.sleep(YELLOW_PAGES_DELAY)
                
            except Exception as e:
                logger.error(f"Error scraping page {page} for {category}: {str(e)}")
                break
                
        return businesses
    
    def _scrape_search_page(self, category: str, location: str, page: int) -> List[Dict[str, Any]]:
        """Scrape a single search results page.
        
        Args:
            category: Business category to search for
            location: Location to search in
            page: Page number to scrape
            
        Returns:
            List of business data dictionaries
        """
        # Construct search URL
        search_url = f"{self.base_url}/search"
        params = {
            'search_terms': category,
            'geo_location_terms': location,
            'page': page
        }
        
        try:
            response = self.session.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find business listings
            businesses = []
            
            # Look for common Yellow Pages listing selectors
            listing_selectors = [
                '.result',
                '.search-results .result',
                '.organic .result',
                '[data-listing-id]',
                '.listing'
            ]
            
            listings = []
            for selector in listing_selectors:
                listings = soup.select(selector)
                if listings:
                    break
            
            for listing in listings:
                business = self._parse_listing(listing)
                if business:
                    businesses.append(business)
                    
            return businesses
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {category} page {page}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error for {category} page {page}: {str(e)}")
            return []
    
    def _parse_listing(self, listing) -> Optional[Dict[str, Any]]:
        """Parse a business listing from HTML.
        
        Args:
            listing: BeautifulSoup element containing business listing
            
        Returns:
            Parsed business data dictionary or None if invalid
        """
        try:
            # Extract business name
            name_selectors = [
                '.business-name',
                '.n',
                'h3 a',
                '.listing-name',
                '[data-business-name]'
            ]
            
            name = ''
            for selector in name_selectors:
                name_elem = listing.select_one(selector)
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    break
            
            if not name:
                return None
            
            # Extract address
            address_selectors = [
                '.street-address',
                '.adr',
                '.address',
                '.locality'
            ]
            
            address_parts = []
            for selector in address_selectors:
                addr_elem = listing.select_one(selector)
                if addr_elem:
                    addr_text = addr_elem.get_text(strip=True)
                    if addr_text:
                        address_parts.append(addr_text)
            
            address = ', '.join(address_parts)
            
            # Extract phone number
            phone_selectors = [
                '.phone',
                '.phones .phone',
                '[data-phone]',
                '.contact-phone'
            ]
            
            phone = ''
            for selector in phone_selectors:
                phone_elem = listing.select_one(selector)
                if phone_elem:
                    phone = phone_elem.get_text(strip=True)
                    # Clean phone number
                    phone = re.sub(r'[^\d\-\(\)\+\s]', '', phone)
                    break
            
            # Extract website
            website_selectors = [
                '.track-visit-website',
                '.website-link',
                'a[href*="website"]',
                '.business-website'
            ]
            
            website = ''
            for selector in website_selectors:
                website_elem = listing.select_one(selector)
                if website_elem:
                    website = website_elem.get('href', '')
                    if website and not website.startswith('http'):
                        website = urljoin(self.base_url, website)
                    break
            
            # Extract business categories/types
            category_selectors = [
                '.categories',
                '.business-categories',
                '.category'
            ]
            
            categories = []
            for selector in category_selectors:
                cat_elems = listing.select(selector)
                for cat_elem in cat_elems:
                    cat_text = cat_elem.get_text(strip=True)
                    if cat_text:
                        categories.append(cat_text)
            
            business_data = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'types': categories,
                'primary_type': categories[0] if categories else '',
                'business_status': 'OPERATIONAL',
                'source': 'yellow_pages'
            }
            
            return business_data
            
        except Exception as e:
            logger.error(f"Error parsing listing: {str(e)}")
            return None

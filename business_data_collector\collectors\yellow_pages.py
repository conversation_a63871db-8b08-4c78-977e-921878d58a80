"""
Yellow Pages scraper for business data using crawl4ai.
"""
import asyncio
import logging
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, quote_plus
from crawl4ai import Async<PERSON>eb<PERSON>raw<PERSON>, JsonCssExtractionStrategy
from config import YELLOW_PAGES_DELAY

logger = logging.getLogger(__name__)


class YellowPagesCollector:
    """Collector for business data from Yellow Pages website using crawl4ai."""

    def __init__(self):
        """Initialize the Yellow Pages collector."""
        self.base_url = "https://www.yellowpages.com"
        self.extraction_strategy = self._setup_extraction_strategy()

    def _setup_extraction_strategy(self):
        """Setup extraction strategy for Yellow Pages business listings."""
        return JsonCssExtractionStrategy({
            "businesses": {
                "selector": ".result, .search-results .result, .organic .result, [data-listing-id], .listing",
                "fields": {
                    "name": {
                        "selector": ".business-name, .n, h3 a, .listing-name, [data-business-name]",
                        "attribute": "text"
                    },
                    "address": {
                        "selector": ".street-address, .adr, .address, .locality",
                        "attribute": "text"
                    },
                    "phone": {
                        "selector": ".phone, .phones .phone, [data-phone], .contact-phone",
                        "attribute": "text"
                    },
                    "website": {
                        "selector": ".track-visit-website, .website-link, a[href*='website'], .business-website",
                        "attribute": "href"
                    },
                    "categories": {
                        "selector": ".categories, .business-categories, .category",
                        "attribute": "text"
                    }
                }
            }
        })
        
    def collect_businesses(self,
                          location: str = "Pittsburgh, PA",
                          categories: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Collect business data from Yellow Pages.

        Args:
            location: Location to search in
            categories: List of business categories to search for

        Returns:
            List of business data dictionaries
        """
        if categories is None:
            categories = [
                "restaurants",
                "retail stores",
                "auto repair",
                "beauty salons",
                "medical services",
                "professional services",
                "home services",
                "financial services"
            ]

        # Run async collection
        return asyncio.run(self._collect_businesses_async(location, categories))

    async def _collect_businesses_async(self, location: str, categories: List[str]) -> List[Dict[str, Any]]:
        """Async method to collect business data from Yellow Pages."""
        businesses = []

        async with AsyncWebCrawler(verbose=False) as crawler:
            for category in categories:
                logger.info(f"Searching Yellow Pages for {category} in {location}...")

                try:
                    category_businesses = await self._search_category_async(crawler, category, location)
                    businesses.extend(category_businesses)

                    # Rate limiting - be respectful
                    await asyncio.sleep(YELLOW_PAGES_DELAY)

                except Exception as e:
                    logger.error(f"Error searching for {category}: {str(e)}")
                    continue

        logger.info(f"Collected {len(businesses)} businesses from Yellow Pages")
        return businesses
    
    async def _search_category_async(self, crawler, category: str, location: str, max_pages: int = 2) -> List[Dict[str, Any]]:
        """Search for businesses in a specific category using crawl4ai.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            max_pages: Maximum number of pages to scrape

        Returns:
            List of business data dictionaries
        """
        businesses = []

        for page in range(1, max_pages + 1):
            try:
                page_businesses = await self._scrape_search_page_async(crawler, category, location, page)
                businesses.extend(page_businesses)

                # If we got fewer results than expected, we've reached the end
                if len(page_businesses) < 5:  # Reduced threshold
                    break

                # Rate limiting between pages
                await asyncio.sleep(YELLOW_PAGES_DELAY)

            except Exception as e:
                logger.error(f"Error scraping page {page} for {category}: {str(e)}")
                break

        return businesses
    
    async def _scrape_search_page_async(self, crawler, category: str, location: str, page: int) -> List[Dict[str, Any]]:
        """Scrape a single search results page using crawl4ai.

        Args:
            crawler: AsyncWebCrawler instance
            category: Business category to search for
            location: Location to search in
            page: Page number to scrape

        Returns:
            List of business data dictionaries
        """
        # Construct search URL
        search_url = f"{self.base_url}/search"
        params = {
            'search_terms': category,
            'geo_location_terms': location,
            'page': page
        }

        # Build full URL with parameters
        param_string = '&'.join([f"{k}={quote_plus(str(v))}" for k, v in params.items()])
        full_url = f"{search_url}?{param_string}"

        try:
            result = await crawler.arun(
                url=full_url,
                extraction_strategy=self.extraction_strategy,
                bypass_cache=True
            )

            if not result.success:
                logger.warning(f"Failed to crawl {full_url}")
                return []

            businesses = []

            # Parse extracted data
            if hasattr(result, 'extracted_content') and result.extracted_content:
                try:
                    import json
                    extracted_data = json.loads(result.extracted_content)

                    if 'businesses' in extracted_data:
                        for business_data in extracted_data['businesses']:
                            business = self._process_extracted_business(business_data)
                            if business:
                                businesses.append(business)

                except (json.JSONDecodeError, KeyError) as e:
                    logger.error(f"Error parsing extracted data: {str(e)}")

            return businesses

        except Exception as e:
            logger.error(f"Error crawling {category} page {page}: {str(e)}")
            return []
    
    def _process_extracted_business(self, business_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process extracted business data from crawl4ai.

        Args:
            business_data: Raw extracted business data

        Returns:
            Processed business data dictionary or None if invalid
        """
        try:
            # Extract and clean business name
            name = business_data.get('name', '').strip()
            if not name:
                return None

            # Extract and clean address
            address = business_data.get('address', '').strip()

            # Extract and clean phone number
            phone = business_data.get('phone', '').strip()
            if phone:
                # Clean phone number
                phone = re.sub(r'[^\d\-\(\)\+\s]', '', phone)

            # Extract and clean website
            website = business_data.get('website', '').strip()
            if website and not website.startswith('http'):
                website = urljoin(self.base_url, website)

            # Extract categories
            categories_raw = business_data.get('categories', '')
            categories = []
            if categories_raw:
                if isinstance(categories_raw, list):
                    categories = [cat.strip() for cat in categories_raw if cat.strip()]
                else:
                    categories = [cat.strip() for cat in str(categories_raw).split(',') if cat.strip()]

            processed_business = {
                'name': name,
                'address': address,
                'phone': phone,
                'website': website,
                'types': categories,
                'primary_type': categories[0] if categories else '',
                'business_status': 'OPERATIONAL',
                'source': 'yellow_pages'
            }

            return processed_business

        except Exception as e:
            logger.error(f"Error processing extracted business: {str(e)}")
            return None

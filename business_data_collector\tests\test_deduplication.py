"""
Tests for business deduplication functionality.
"""
import unittest
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.deduplication import BusinessDeduplicator


class TestBusinessDeduplicator(unittest.TestCase):
    """Test cases for BusinessDeduplicator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.deduplicator = BusinessDeduplicator()
        
    def test_exact_duplicate_removal(self):
        """Test removal of exact duplicates."""
        business1 = {
            'name': '<PERSON>\'s Pizza',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '************',
            'source': 'google_places'
        }
        
        business2 = {
            'name': '<PERSON>\'s Pizza',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '************',
            'source': 'overpass_api'
        }
        
        result = self.deduplicator.deduplicate_businesses([business1, business2])
        self.assertEqual(len(result), 1)
        # Should merge sources
        self.assertIn('google_places', result[0]['source'])
        self.assertIn('overpass_api', result[0]['source'])
        
    def test_fuzzy_duplicate_removal(self):
        """Test removal of fuzzy duplicates."""
        business1 = {
            'name': 'Joe\'s Pizza Restaurant',
            'address': '123 Main Street, Pittsburgh, PA',
            'phone': '************',
            'source': 'google_places'
        }
        
        business2 = {
            'name': 'Joes Pizza',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '(*************',
            'source': 'yellow_pages'
        }
        
        result = self.deduplicator.deduplicate_businesses([business1, business2])
        self.assertEqual(len(result), 1)
        
    def test_different_businesses_kept(self):
        """Test that different businesses are kept separate."""
        business1 = {
            'name': 'Joe\'s Pizza',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '************',
            'source': 'google_places'
        }
        
        business2 = {
            'name': 'Maria\'s Italian',
            'address': '456 Oak Ave, Pittsburgh, PA',
            'phone': '************',
            'source': 'google_places'
        }
        
        result = self.deduplicator.deduplicate_businesses([business1, business2])
        self.assertEqual(len(result), 2)
        
    def test_normalize_text(self):
        """Test text normalization."""
        test_cases = [
            ('Joe\'s Pizza Restaurant', 'joe\'s pizza'),
            ('THE BEST COFFEE SHOP LLC', 'best coffee'),
            ('ABC-123 Store & More!!!', 'abc 123 store more'),
        ]

        for input_text, expected in test_cases:
            result = self.deduplicator._normalize_text(input_text)
            self.assertEqual(result, expected)
            
    def test_normalize_phone(self):
        """Test phone number normalization."""
        test_cases = [
            ('(*************', '4125550123'),
            ('************', '4125550123'),
            ('1-************', '4125550123'),
            ('****** 555 0123', '4125550123'),
        ]
        
        for input_phone, expected in test_cases:
            result = self.deduplicator._normalize_phone(input_phone)
            self.assertEqual(result, expected)
            
    def test_merge_business_data(self):
        """Test business data merging."""
        target = {
            'name': 'Joe\'s Pizza',
            'address': '123 Main St',
            'phone': '',
            'website': '',
            'source': 'google_places'
        }
        
        source = {
            'name': 'Joe\'s Pizza',
            'address': '123 Main St',
            'phone': '************',
            'website': 'https://joespizza.com',
            'source': 'yellow_pages'
        }
        
        self.deduplicator._merge_business_data(target, source)
        
        self.assertEqual(target['phone'], '************')
        self.assertEqual(target['website'], 'https://joespizza.com')
        self.assertIn('google_places', target['source'])
        self.assertIn('yellow_pages', target['source'])


if __name__ == '__main__':
    unittest.main()

"""
Tests for business filtering functionality.
"""
import unittest
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.filtering import BusinessFilter


class TestBusinessFilter(unittest.TestCase):
    """Test cases for BusinessFilter class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.filter = BusinessFilter()
        
    def test_filter_valid_local_business(self):
        """Test that valid local businesses pass the filter."""
        business = {
            'name': '<PERSON>\'s Pizza',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '************',
            'website': 'https://joespizza.com',
            'source': 'google_places'
        }
        
        result = self.filter.filter_businesses([business])
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['name'], '<PERSON>\'s Pizza')
        
    def test_filter_corporate_chain(self):
        """Test that corporate chains are filtered out."""
        business = {
            'name': '<PERSON>\'s',
            'address': '456 Corporate Ave, Pittsburgh, PA',
            'phone': '************',
            'website': 'https://mcdonalds.com',
            'source': 'google_places'
        }
        
        result = self.filter.filter_businesses([business])
        self.assertEqual(len(result), 0)
        
    def test_filter_missing_required_fields(self):
        """Test that businesses missing required fields are filtered out."""
        # Business with no name
        business1 = {
            'name': '',
            'address': '123 Main St, Pittsburgh, PA',
            'phone': '************',
            'source': 'google_places'
        }
        
        # Business with no contact info
        business2 = {
            'name': 'Test Business',
            'address': '',
            'phone': '',
            'website': '',
            'source': 'google_places'
        }
        
        result = self.filter.filter_businesses([business1, business2])
        self.assertEqual(len(result), 0)
        
    def test_normalize_name(self):
        """Test name normalization functionality."""
        test_cases = [
            ('Joe\'s Pizza Restaurant', 'joe\'s pizza'),
            ('The Best Coffee Shop LLC', 'best coffee'),
            ('ABC Corporation', 'abc'),
            ('Family Services Inc.', 'family'),
        ]

        for input_name, expected in test_cases:
            result = self.filter._normalize_name(input_name)
            self.assertEqual(result, expected)
            
    def test_appears_local(self):
        """Test local business detection."""
        # Local business
        local_business = {
            'name': 'Pittsburgh Family Restaurant',
            'address': '123 Main St, Pittsburgh, PA 15201',
            'phone': '************'
        }
        
        # Non-local business
        corporate_business = {
            'name': 'Generic Restaurant',
            'address': '456 Corporate Ave, Anywhere, USA',
            'phone': '************'
        }
        
        self.assertTrue(self.filter._appears_local(local_business))
        self.assertFalse(self.filter._appears_local(corporate_business))


if __name__ == '__main__':
    unittest.main()

"""
Integration tests for the business data collector.
"""
import unittest
import sys
import os
import tempfile
import shutil

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.filtering import BusinessFilter
from utils.deduplication import BusinessDeduplicator
from utils.data_export import DataExporter


class TestIntegration(unittest.TestCase):
    """Integration test cases."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.sample_businesses = [
            {
                'name': 'Joe\'s Pizza',
                'address': '123 Main St, Pittsburgh, PA',
                'phone': '************',
                'website': 'https://joespizza.com',
                'types': ['restaurant', 'pizza'],
                'source': 'google_places'
            },
            {
                'name': 'Joes Pizza Restaurant',  # Similar to above
                'address': '123 Main Street, Pittsburgh, PA',
                'phone': '(*************',
                'website': '',
                'types': ['restaurant'],
                'source': 'yellow_pages'
            },
            {
                'name': 'McDonald\'s',  # Should be filtered out
                'address': '456 Corporate Ave, Pittsburgh, PA',
                'phone': '************',
                'website': 'https://mcdonalds.com',
                'types': ['restaurant', 'fast_food'],
                'source': 'google_places'
            },
            {
                'name': 'Local Coffee Shop',
                'address': '789 Oak St, Pittsburgh, PA',
                'phone': '************',
                'website': 'https://localcoffee.com',
                'types': ['cafe', 'coffee'],
                'source': 'overpass_api'
            },
            {
                'name': '',  # Should be filtered out (no name)
                'address': '999 Empty St, Pittsburgh, PA',
                'phone': '************',
                'website': '',
                'types': [],
                'source': 'yellow_pages'
            }
        ]
        
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)
        
    def test_full_processing_pipeline(self):
        """Test the complete processing pipeline."""
        # Filter businesses
        business_filter = BusinessFilter()
        filtered_businesses = business_filter.filter_businesses(self.sample_businesses)
        
        # Should filter out McDonald's and the business with no name
        self.assertEqual(len(filtered_businesses), 3)  # Joe's (x2) + Local Coffee Shop
        
        # Deduplicate businesses
        deduplicator = BusinessDeduplicator()
        unique_businesses = deduplicator.deduplicate_businesses(filtered_businesses)
        
        # Should merge the two Joe's Pizza entries
        self.assertEqual(len(unique_businesses), 2)  # Joe's + Local Coffee Shop
        
        # Check that Joe's Pizza data was merged
        joes_pizza = next((b for b in unique_businesses if 'joe' in b['name'].lower()), None)
        self.assertIsNotNone(joes_pizza)
        self.assertEqual(joes_pizza['phone'], '************')
        self.assertEqual(joes_pizza['website'], 'https://joespizza.com')
        self.assertIn('google_places', joes_pizza['source'])
        self.assertIn('yellow_pages', joes_pizza['source'])
        
    def test_export_functionality(self):
        """Test data export functionality."""
        # Process businesses first
        business_filter = BusinessFilter()
        filtered_businesses = business_filter.filter_businesses(self.sample_businesses)
        
        deduplicator = BusinessDeduplicator()
        unique_businesses = deduplicator.deduplicate_businesses(filtered_businesses)
        
        # Export to CSV
        exporter = DataExporter(self.temp_dir)
        csv_path = exporter.export_to_csv(unique_businesses, 'test_businesses.csv')
        
        self.assertIsNotNone(csv_path)
        self.assertTrue(os.path.exists(csv_path))
        
        # Check CSV content
        with open(csv_path, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('Joe\'s Pizza', content)
            self.assertIn('Local Coffee Shop', content)
            self.assertNotIn('McDonald\'s', content)  # Should be filtered out
            
        # Export to JSON
        json_path = exporter.export_to_json(unique_businesses, 'test_businesses.json')
        
        self.assertIsNotNone(json_path)
        self.assertTrue(os.path.exists(json_path))
        
        # Export summary report
        report_path = exporter.export_summary_report(unique_businesses, 'test_report.txt')
        
        self.assertIsNotNone(report_path)
        self.assertTrue(os.path.exists(report_path))
        
        # Check report content
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('Total Businesses Collected: 2', content)
            self.assertIn('google_places', content)
            self.assertIn('overpass_api', content)


if __name__ == '__main__':
    unittest.main()
